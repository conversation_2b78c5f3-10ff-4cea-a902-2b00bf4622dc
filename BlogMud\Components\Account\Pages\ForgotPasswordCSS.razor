@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using BlogMud.Data

<style>
    /* Enhanced Background with Floating Particles */
    .forgot-password-container {
        min-height: calc(100vh - 120px);
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 1rem;
        position: relative;
        overflow: hidden;
        animation: backgroundShift 10s ease-in-out infinite;
    }

    @keyframes backgroundShift {
        0%, 100% { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%); }
        50% { background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 50%, #e9ecef 100%); }
    }

    /* Floating Particles Animation */
    .forgot-password-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: 
            radial-gradient(circle at 20% 80%, rgba(149, 55, 53, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(149, 55, 53, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(149, 55, 53, 0.05) 0%, transparent 50%);
        animation: particleFloat 15s ease-in-out infinite;
        pointer-events: none;
    }

    @keyframes particleFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
        50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
    }

    /* Enhanced Forgot Password Card with Glassmorphism */
    .forgot-password-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 24px;
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.1),
            0 8px 32px rgba(149, 55, 53, 0.05),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
        overflow: hidden;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        max-width: 500px;
        width: 100%;
        position: relative;
        z-index: 1;
        animation: cardEntrance 0.8s ease-out;
    }

    .forgot-password-card:hover {
        box-shadow:
            0 30px 90px rgba(0, 0, 0, 0.15),
            0 12px 40px rgba(149, 55, 53, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
        transform: translateY(-4px) scale(1.01);
        border-color: rgba(149, 55, 53, 0.1);
    }

    @keyframes cardEntrance {
        0% {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
        }
        100% {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Enhanced Header Section */
    .forgot-password-header {
        background: linear-gradient(135deg,
            var(--mud-palette-primary) 0%,
            var(--mud-palette-primary-darken) 100%);
        color: white;
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .forgot-password-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: 
            radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
        animation: headerGlow 8s ease-in-out infinite;
        pointer-events: none;
    }

    @keyframes headerGlow {
        0%, 100% { opacity: 0.7; transform: scale(1); }
        50% { opacity: 1; transform: scale(1.05); }
    }

    .forgot-password-header-content {
        position: relative;
        z-index: 1;
    }

    .forgot-password-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        animation: iconPulse 3s ease-in-out infinite;
    }

    @keyframes iconPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    /* Enhanced Form Section */
    .forgot-password-form-section {
        padding: 2.5rem 2rem;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
    }

    /* Enhanced Form Input Styling */
    .form-input {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .form-input .mud-input-control {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 12px;
    }

    .form-input .mud-input-control:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(149, 55, 53, 0.1);
    }

    .form-input .mud-input-control:focus-within {
        transform: translateY(-3px) scale(1.02);
        box-shadow: 
            0 12px 35px rgba(149, 55, 53, 0.15),
            0 0 0 3px rgba(149, 55, 53, 0.1);
    }

    .form-input .mud-input-adornment {
        transition: all 0.3s ease;
        color: var(--mud-palette-primary);
    }

    .form-input .mud-input-control:focus-within .mud-input-adornment {
        color: var(--mud-palette-primary-darken);
        transform: scale(1.1);
    }

    /* Enhanced Forgot Password Button with Advanced Effects */
    .forgot-password-button {
        height: 52px;
        border-radius: 16px;
        font-weight: 600;
        text-transform: none;
        background: linear-gradient(135deg,
            var(--mud-palette-primary) 0%,
            var(--mud-palette-primary-darken) 100%);
        box-shadow:
            0 6px 20px rgba(149, 55, 53, 0.3),
            0 2px 8px rgba(149, 55, 53, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        letter-spacing: 0.5px;
    }

    .forgot-password-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.2) 50%,
            transparent 100%);
        transition: left 0.6s ease;
    }

    .forgot-password-button:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow:
            0 12px 35px rgba(149, 55, 53, 0.4),
            0 6px 15px rgba(149, 55, 53, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .forgot-password-button:hover::before {
        left: 100%;
    }

    .forgot-password-button:active {
        transform: translateY(-1px) scale(1.01);
        box-shadow:
            0 6px 20px rgba(149, 55, 53, 0.3),
            0 2px 8px rgba(149, 55, 53, 0.2);
    }

    /* Enhanced Navigation Links */
    .forgot-password-navigation {
        text-align: center;
        margin-top: 1rem;
    }

    .navigation-link {
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        background: rgba(149, 55, 53, 0.05);
        margin: 0.25rem;
    }

    .navigation-link:hover {
        background: rgba(149, 55, 53, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(149, 55, 53, 0.2);
    }

    /* Enhanced Responsive Design */
    @media (max-width: 600px) {
        .forgot-password-container {
            padding: 1rem 0.5rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .forgot-password-header {
            padding: 2rem 1.5rem;
        }

        .forgot-password-form-section {
            padding: 2rem 1.5rem;
        }

        .forgot-password-icon {
            font-size: 3rem;
        }

        .forgot-password-card {
            border-radius: 20px;
            margin: 0.5rem;
        }

        .form-input .mud-input-control:focus-within {
            transform: translateY(-1px) scale(1.01);
        }

        .forgot-password-button {
            height: 48px;
        }
    }

    @media (max-width: 400px) {
        .forgot-password-header {
            padding: 1.5rem 1rem;
        }

        .forgot-password-form-section {
            padding: 1.5rem 1rem;
        }

        .forgot-password-icon {
            font-size: 2.5rem;
        }

        .forgot-password-card {
            border-radius: 16px;
        }
    }

    /* Enhanced Text Field Styling */
    .mud-input-outlined .mud-input-outlined-border {
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .mud-input-outlined:hover .mud-input-outlined-border {
        border-color: rgba(149, 55, 53, 0.3);
    }

    .mud-input-outlined.mud-input-focused .mud-input-outlined-border {
        border-color: var(--mud-palette-primary);
        border-width: 2px;
        box-shadow: 0 0 0 3px rgba(149, 55, 53, 0.1);
    }

    /* Enhanced Divider Styling */
    .mud-divider {
        background: linear-gradient(90deg,
            transparent 0%,
            rgba(149, 55, 53, 0.2) 50%,
            transparent 100%);
        height: 1px;
        margin: 1.5rem 0;
    }
</style>
