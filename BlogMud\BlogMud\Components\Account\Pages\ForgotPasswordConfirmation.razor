﻿@page "/Account/ForgotPasswordConfirmation"

<PageTitle>تأكيد إعادة تعيين كلمة المرور</PageTitle>
<ForgotPasswordConfirmationCSS />

<div class="confirmation-container">
    <MudContainer MaxWidth="MaxWidth.Small" Class="confirmation-content">
        <MudPaper Class="confirmation-card" Elevation="0">
            <!-- Header Section -->
            <div class="confirmation-header">
                <div class="confirmation-header-content">
                    <MudIcon Icon="@Icons.Material.Filled.MarkEmailRead" Class="confirmation-icon" />
                    <MudText Typo="Typo.h4" Class="mb-2" Style="font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        تم إرسال الرابط بنجاح!
                    </MudText>
                    <MudText Typo="Typo.body1" Style="opacity: 0.85; font-weight: 400; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                        تحقق من بريدك الإلكتروني لإعادة تعيين كلمة المرور
                    </MudText>
                </div>
            </div>

            <!-- Content Section -->
            <div class="confirmation-content-section">
                <MudAlert Severity="Severity.Success" Variant="Variant.Outlined" Class="mb-4">
                    <div style="display: flex; align-items: center;">
                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="mr-2" />
                        <div>
                            <MudText Typo="Typo.body1" Style="font-weight: 600;">تم إرسال رابط إعادة التعيين</MudText>
                            <MudText Typo="Typo.body2" Style="opacity: 0.8;">لقد أرسلنا رابط آمن إلى بريدك الإلكتروني</MudText>
                        </div>
                    </div>
                </MudAlert>

                <MudText Typo="Typo.body1" Class="mb-4" Style="line-height: 1.6; color: #495057;">
                    يرجى التحقق من صندوق الوارد الخاص بك (وصندوق الرسائل غير المرغوب فيها) للعثور على رسالة إعادة تعيين كلمة المرور.
                </MudText>

                <MudPaper Elevation="1" Class="pa-4 mb-4" Style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px;">
                    <MudText Typo="Typo.h6" Class="mb-3" Style="color: var(--mud-palette-primary); font-weight: 600;">
                        <MudIcon Icon="@Icons.Material.Filled.Info" Class="mr-2" />
                        خطوات إعادة تعيين كلمة المرور:
                    </MudText>

                    <MudList T="bool">
                        <MudListItem>
                            <div style="display: flex; align-items: center;">
                                <MudAvatar Color="Color.Primary" Size="Size.Small" Class="mr-3">1</MudAvatar>
                                <MudText Typo="Typo.body2">تحقق من بريدك الإلكتروني</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div style="display: flex; align-items: center;">
                                <MudAvatar Color="Color.Primary" Size="Size.Small" Class="mr-3">2</MudAvatar>
                                <MudText Typo="Typo.body2">انقر على رابط إعادة التعيين</MudText>
                            </div>
                        </MudListItem>
                        <MudListItem>
                            <div style="display: flex; align-items: center;">
                                <MudAvatar Color="Color.Primary" Size="Size.Small" Class="mr-3">3</MudAvatar>
                                <MudText Typo="Typo.body2">أدخل كلمة المرور الجديدة</MudText>
                            </div>
                        </MudListItem>
                    </MudList>
                </MudPaper>

                <MudAlert Severity="Severity.Info" Variant="Variant.Text" Class="mb-4">
                    <MudText Typo="Typo.body2">
                        <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" Class="mr-1" />
                        الرابط صالح لمدة 24 ساعة فقط لأسباب أمنية
                    </MudText>
                </MudAlert>

                <MudDivider Class="my-4" />

                <!-- Action Buttons -->
                <div class="confirmation-actions">
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               FullWidth="true"
                               Size="Size.Large"
                               Class="confirmation-button mb-3"
                               Href="/Account/Login">
                        <MudIcon Icon="@Icons.Material.Filled.Login" Class="mr-2" />
                        العودة إلى تسجيل الدخول
                    </MudButton>

                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               FullWidth="true"
                               Size="Size.Medium"
                               Class="resend-button"
                               Href="/Account/ForgotPassword">
                        <MudIcon Icon="@Icons.Material.Filled.Refresh" Class="mr-2" />
                        إعادة إرسال الرابط
                    </MudButton>
                </div>

                <!-- Help Section -->
                <MudText Typo="Typo.body2" Align="Align.Center" Class="mt-4" Style="color: #6c757d;">
                    لم تستلم الرسالة؟ تحقق من مجلد الرسائل غير المرغوب فيها أو
                    <MudLink Href="/Contact" Color="Color.Primary">اتصل بنا</MudLink>
                </MudText>
            </div>
        </MudPaper>
    </MudContainer>
</div>
