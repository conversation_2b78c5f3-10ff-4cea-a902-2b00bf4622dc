﻿@page "/Account/ForgotPassword"

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using BlogMud.Data

@inject UserManager<ApplicationUser> UserManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager

@rendermode InteractiveServer

<PageTitle>نسيت كلمة المرور؟</PageTitle>
<ForgotPasswordCSS />

<div class="forgot-password-container">
    <MudContainer MaxWidth="MaxWidth.Small" Class="forgot-password-content">
        <MudPaper Class="forgot-password-card" Elevation="0">
            <!-- Header Section -->
            <div class="forgot-password-header">
                <div class="forgot-password-header-content">
                    <MudIcon Icon="@Icons.Material.Filled.LockReset" Class="forgot-password-icon" />
                    <MudText Typo="Typo.h4" Class="mb-2" Style="font-weight: 700; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        نسيت كلمة المرور؟
                    </MudText>
                    <MudText Typo="Typo.body1" Style="opacity: 0.85; font-weight: 400; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">
                        لا تقلق، سنساعدك في استعادة الوصول إلى حسابك
                    </MudText>
                    <div style="margin-top: 1rem; opacity: 0.7;">
                        <MudIcon Icon="@Icons.Material.Filled.Email" Size="Size.Small" Style="margin: 0 0.5rem;" />
                        <MudIcon Icon="@Icons.Material.Filled.Security" Size="Size.Small" Style="margin: 0 0.5rem;" />
                        <MudIcon Icon="@Icons.Material.Filled.Verified" Size="Size.Small" Style="margin: 0 0.5rem;" />
                    </div>
                </div>
            </div>

            <!-- Form Section -->
            <div class="forgot-password-form-section">
                <EditForm Model="Input" FormName="forgot-password" OnValidSubmit="OnValidSubmitAsync" method="post">
                    <DataAnnotationsValidator />

                    <MudText Typo="Typo.h6" Class="mb-4" Style="color: var(--mud-palette-primary); font-weight: 600;">
                        <MudIcon Icon="@Icons.Material.Filled.Email" Class="mr-2" />
                        أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور
                    </MudText>

                    <MudText Typo="Typo.body2" Class="mb-4" Style="color: #6c757d; line-height: 1.6;">
                        سنرسل لك رابط آمن عبر البريد الإلكتروني لإعادة تعيين كلمة المرور الخاصة بك.
                    </MudText>

                    <div class="form-input">
                        <MudTextField @bind-Value="Input.Email"
                                      For="@(() => Input.Email)"
                                      Label="البريد الإلكتروني"
                                      Placeholder="<EMAIL>"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Normal"
                                      FullWidth="true"
                                      Required="true"
                                      RequiredError="البريد الإلكتروني مطلوب"
                                      Validation="@(new EmailAddressAttribute() { ErrorMessage = "البريد الإلكتروني غير صحيح" })"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Email"
                                      AdornmentColor="Color.Primary"
                                      UserAttributes="@(new() { { "autocomplete", "email" }, { "aria-required", "true" } } )"
                                      Class="mb-4" />
                    </div>

                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               FullWidth="true"
                               Size="Size.Large"
                               Class="forgot-password-button mb-4"
                               Disabled="@_processing"
                               ButtonType="ButtonType.Submit">
                        @if (_processing)
                        {
                            <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                            <MudText Class="ms-2">جاري الإرسال...</MudText>
                        }
                        else
                        {
                            <MudIcon Icon="@Icons.Material.Filled.Send" Class="mr-2" />
                            <span>إرسال رابط إعادة التعيين</span>
                        }
                    </MudButton>
                </EditForm>

                <MudDivider Class="my-4" />

                <!-- Navigation Links -->
                <div class="forgot-password-navigation">
                    <MudText Typo="Typo.body2" Align="Align.Center" Class="mb-3">
                        <MudLink Href="/Account/Login" Color="Color.Primary" Class="navigation-link">
                            <MudIcon Icon="@Icons.Material.Filled.ArrowBack" Size="Size.Small" Class="mr-1" />
                            العودة إلى تسجيل الدخول
                        </MudLink>
                    </MudText>

                    <MudText Typo="Typo.body2" Align="Align.Center">
                        ليس لديك حساب؟
                        <MudLink Href="/Account/Register" Color="Color.Primary" Class="navigation-link">
                            إنشاء حساب جديد
                        </MudLink>
                    </MudText>
                </div>
            </div>
        </MudPaper>
    </MudContainer>
</div>

@code {
    private bool _processing = false;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    private async Task OnValidSubmitAsync()
    {
        if (_processing) return;

        _processing = true;
        StateHasChanged();

        try
        {
            var user = await UserManager.FindByEmailAsync(Input.Email);
            if (user is null || !(await UserManager.IsEmailConfirmedAsync(user)))
            {
                // Don't reveal that the user does not exist or is not confirmed
                RedirectManager.RedirectTo("Account/ForgotPasswordConfirmation");
                return;
            }

            // For more information on how to enable account confirmation and password reset please
            // visit https://go.microsoft.com/fwlink/?LinkID=532713
            var code = await UserManager.GeneratePasswordResetTokenAsync(user);
            code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
            var callbackUrl = NavigationManager.GetUriWithQueryParameters(
                NavigationManager.ToAbsoluteUri("Account/ResetPassword").AbsoluteUri,
                new Dictionary<string, object?> { ["code"] = code });

            await EmailSender.SendPasswordResetLinkAsync(user, Input.Email, HtmlEncoder.Default.Encode(callbackUrl));

            RedirectManager.RedirectTo("Account/ForgotPasswordConfirmation");
        }
        catch (Exception)
        {
            // In case of any error, still redirect to confirmation page for security
            RedirectManager.RedirectTo("Account/ForgotPasswordConfirmation");
        }
        finally
        {
            _processing = false;
            StateHasChanged();
        }
    }

    private sealed class InputModel
    {
        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = "";
    }
}
